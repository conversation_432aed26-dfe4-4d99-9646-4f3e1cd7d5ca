import { Jurisdiction, ShortJurisdiction } from "../entities/jurisdiction";
import { Op, Transaction, UniqueConstraintError, WhereOptions } from "sequelize";
import { EntityJurisdictionDBInstance, } from "../models/jurisdiction";

import * as Errors from "../errors";
import { BaseEntity, ChildEntity, Entity, EntityInfo, Includable } from "../entities/entity";
import { getJurisdictionService, JurisdictionService, toInfo as jurisdictionToInfo } from "./jurisdiction";
import { lazy } from "@skywind-group/sw-utils";
import { PagingHelper } from "../utils/paginghelper";
import { getFlatEntities } from "./entity";
import { Models } from "../models/models";

const JurisdictionModel = Models.JurisdictionModel;
const EntityJurisdictionModel = Models.EntityJurisdictionModel;

const container = lazy<EntityJurisdictionService & Includable<{ jurisdiction?: ShortJurisdiction[] }>>(
    () => new EntityJurisdictionServiceImpl(getJurisdictionService()));

export function getEntityJurisdictionService(): EntityJurisdictionService
    & Includable<{ jurisdiction?: ShortJurisdiction[] }> {
    return container.get();
}

export interface EntityJurisdictionService {
    add(entity: BaseEntity, jurisdictionCode: string, transaction?: Transaction): Promise<Jurisdiction>;

    remove(entity: BaseEntity, jurisdictionCode: string, transaction?: Transaction): Promise<void>;

    findAll(query: WhereOptions<any>, limit?: number, offset?: number): Promise<Jurisdiction[]>;

    findOne(entityId: number, jurisdictionCode, raiseErrorIfNotFound?: boolean): Promise<Jurisdiction>;

    findOneByEntityId(entityId: number): Promise<Jurisdiction>;
}

class EntityJurisdictionServiceImpl implements EntityJurisdictionService,
    Includable<{ jurisdiction?: ShortJurisdiction[] }> {
    public static model = EntityJurisdictionModel;

    constructor(private jurisdictionService: JurisdictionService) {
    }

    public async add(entity: BaseEntity, jurisdictionCode: string, transaction?: Transaction): Promise<Jurisdiction> {
        const jurisdiction: Jurisdiction = await this.jurisdictionService.findOne(jurisdictionCode);

        await this.checkJurisdictionInParent(entity, jurisdictionCode);
        await this.validateDefaultCountryOnCreate(entity, jurisdiction);

        try {
            // Brand must have no more than one jurisdiction
            if (entity.isBrand()) {
                await EntityJurisdictionServiceImpl.model.destroy({ where: { entityId: entity.id }, transaction });
            }

            await EntityJurisdictionServiceImpl.model.create({
                jurisdictionId: jurisdiction.id,
                entityId: entity.id
            }, { transaction });

            return jurisdiction;
        } catch (err) {
            if (err instanceof UniqueConstraintError) {
                throw new Errors.ValidationError("Jurisdiction already exist in entity");
            }

            throw err;
        }
    }

    private async checkJurisdictionInParent(entity: BaseEntity, jurisdictionCode: string): Promise<void> {
        const parent = (entity as ChildEntity).getParent();

        if (parent.isMaster()) {
            // Master has all jurisdictions
            return;
        }

        try {
            await this.findOne(parent.id, jurisdictionCode);
        } catch (error) {
            if (error instanceof Errors.ValidationError) {
                throw new Errors.ValidationError("Jurisdiction not exist in parent");
            }
        }
    }

    public async findOne(entityId: number,
                         jurisdictionCode: string,
                         raiseErrorIfNotFound: boolean = true): Promise<Jurisdiction> {
        const jurisdiction: Jurisdiction = await this.jurisdictionService.findOne(jurisdictionCode);

        const sqlQuery = {
            where: {
                jurisdictionId: jurisdiction.id,
                entityId
            }
        };
        const instance = await EntityJurisdictionServiceImpl.model.findOne(sqlQuery);

        if (raiseErrorIfNotFound && !instance) {
            throw new Errors.ValidationError("Jurisdiction not found for entity");
        }

        return jurisdiction;
    }

    private async checkBrandRestriction(entity: BaseEntity): Promise<void> {
        if (!entity.isBrand()) {
            return;
        }

        const count = await EntityJurisdictionServiceImpl.model.count({
            where: {
                entityId: entity.id
            }
        });

        if (count) {
            throw new Errors.ValidationError("Brand must have no more than one jurisdiction");
        }
    }

    public async remove(entity: BaseEntity, jurisdictionCode: string, transaction?: Transaction): Promise<void> {
        if (entity.isBrand()) {
            throw new Errors.ValidationError("You cannot remove jurisdiction from Brand");
        }

        const jurisdiction: Jurisdiction = await this.jurisdictionService.findOne(jurisdictionCode);

        await this.checkJurisdictionInChildren(entity, jurisdiction.id);
        await this.validateDefaultCountryOnRemoval(entity, jurisdiction);

        await EntityJurisdictionServiceImpl.model.destroy({
            where: {
                jurisdictionId: jurisdiction.id,
                entityId: entity.id
            },
            transaction: transaction
        });
    }

    private async checkJurisdictionInChildren(entity: BaseEntity, jurisdictionId: number): Promise<void> {
        const children = (entity as Entity).child;
        if (!(children && children.length)) {
            return;
        }

        const count = await EntityJurisdictionServiceImpl.model.count({
            where: {
                jurisdictionId: jurisdictionId,
                entityId: {
                    [Op.in]: children.map(c => c.id)
                }
            }
        });

        if (count) {
            throw new Errors.ValidationError("Jurisdiction exist in child entity");
        }
    }

    public async findAll(query: WhereOptions<any>, limit = 100, offset = 0): Promise<Jurisdiction[]> {
        return PagingHelper.findAndCountAll(EntityJurisdictionServiceImpl.model, {
            where: query,
            include: [
                {
                    model: JurisdictionModel, as: "jurisdiction"
                }
            ],
            offset,
            limit
        }, (item) => toInfo(item));
    }

    public async findOneByEntityId(entityId: number): Promise<Jurisdiction> {
        const [jurisdiction] = await this.findAll({ entityId });

        return jurisdiction;
    }

    /*
        Include to operator entity jurisdiction info - title and code.
     */
    public async includeTo(entity: EntityInfo): Promise<EntityInfo & { jurisdiction?: ShortJurisdiction[] }> {
        const flatEntities = getFlatEntities(entity);
        const jurisdictions = await this.findAll({
            entityId: {
                [Op.in]: flatEntities.map(currentEntity => currentEntity.id)
            }
        }, null);

        for (const flattenEntityInfo of flatEntities) {
            // calculate array of jurisdictions
            const entityJurisdictions = jurisdictions.reduce((jurisdictionsAccumulator, jurisdictionItem) => {
                if (jurisdictionItem.entityId === flattenEntityInfo.id) {
                    jurisdictionsAccumulator.push({ code: jurisdictionItem.code, title: jurisdictionItem.title });
                }
                return jurisdictionsAccumulator;
            }, []);
            // attach it to entity
            flattenEntityInfo.jurisdiction = entityJurisdictions;
        }

        return entity;
    }

    private async validateDefaultCountryOnCreate(entity: BaseEntity, jurisdiction: Jurisdiction): Promise<void> {
        // Only validate if the jurisdiction being added has no defaultCountry
        // In this case, we need to ensure the entity has a defaultCountry OR there are existing jurisdictions with defaultCountry
        if (!jurisdiction.defaultCountry) {
            await this.validateDefaultCountry(entity as ChildEntity, jurisdiction);
        }
    }

    private async validateDefaultCountryOnRemoval(entity: BaseEntity, jurisdiction: Jurisdiction): Promise<void> {
        if (jurisdiction.defaultCountry) {
            await this.validateDefaultCountry(entity as ChildEntity, jurisdiction);
        }
    }

    // Check if there are other jurisdictions for this entity that have defaultCountry
    private async validateDefaultCountry(entity: ChildEntity, jurisdiction: Jurisdiction) {
        if (!entity.defaultCountry) {
            const count = await EntityJurisdictionServiceImpl.model.count({
                where: {
                    entityId: entity.id,
                    jurisdictionId: {
                        [Op.ne]: jurisdiction.id
                    }
                },
                include: [
                    {
                        model: JurisdictionModel,
                        as: "jurisdiction",
                        where: {
                            defaultCountry: {
                                [Op.ne]: null
                            }
                        }
                    }
                ]
            });
            if (count === 0) {
                throw new Errors.ValidationError("Default country should be defined in jurisdiction or entity");
            }
        }
    }
}

function toInfo(instance: EntityJurisdictionDBInstance): Jurisdiction {
    return jurisdictionToInfo(instance.get("jurisdiction"), instance.get("entityId"));
}
